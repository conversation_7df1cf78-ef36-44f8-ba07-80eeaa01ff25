package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Return;

/**
 * @ClassName ReturnService
 * @Description TODO
 * @date 2025年8月7日 下午4:21:34
 * <AUTHOR>
 * @version 1.0
 */
public interface ReturnService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	Integer save(Return record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	Integer update(Return record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return Return
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	Return selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<Return>
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	DataSet<Return> getDataSetList(Page page, Return record);
}
