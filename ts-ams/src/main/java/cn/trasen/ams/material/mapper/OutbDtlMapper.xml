<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.OutbDtlMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.OutbDtl">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outb_id" jdbcType="VARCHAR" property="outbId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO m_outb_dtl (
      id,
      outb_id,
      sku_id,
      batch_no,
      price,
      num,
      total_amt,
      create_date,
      create_user,
      create_user_name,
      dept_id,
      dept_name,
      update_date,
      update_user,
      update_user_name,
      sso_org_code,
      sso_org_name,
      is_deleted
    ) VALUES
    <foreach collection="outbDtlList" item="item" separator=",">
      (
        #{item.id},
        #{item.outbId},
        #{item.skuId},
        #{item.batchNo},
        #{item.price},
        #{item.num},
        #{item.totalAmt},
        #{item.createDate},
        #{item.createUser},
        #{item.createUserName},
        #{item.deptId},
        #{item.deptName},
        #{item.updateDate},
        #{item.updateUser},
        #{item.updateUserName},
        #{item.ssoOrgCode},
        #{item.ssoOrgName},
        #{item.isDeleted}
      )
    </foreach>
  </insert>

  <select id="getOutbDtlExtListByOutbId" resultType="cn.trasen.ams.material.bean.outb.OutbDtlResp" parameterType="cn.trasen.ams.material.bean.outb.OutbDtlResp">
      SELECT
      t1.*,
      t2.flow_no,
      t2.name,
      t2.model,
      t2.unit,
      COALESCE(t7.price, t2.price) AS origin_price,
      t2.reg_no,
      t2.brand,
      t3.name as category_name,
      t4.name as manufacturer_name,
      t5.wh_id,
      COALESCE(t8.num, t6.num,0) as stock,
      t7.prod_no,
      t7.prod_date,
      t7.expire_date
      FROM m_outb_dtl t1
      LEFT JOIN m_sku t2 ON t1.sku_id = t2.id AND t2.is_deleted = 'N'
      LEFT JOIN c_category t3 ON t2.category_id = t3.id AND t3.is_deleted = 'N'
      LEFT JOIN c_manufacturer t4 ON t2.manufacturer_id = t4.id AND t4.is_deleted = 'N'
      LEFT JOIN m_outb t5 ON t1.outb_id = t5.id AND t5.is_deleted = 'N'
      LEFT JOIN (
      select
      t1.sku_id,
      t1.wh_id,
      sum(COALESCE(t1.`num`,0)) as num,
      '' as batch_no,
      0 as price,
      '' as prod_no,
      null as prod_date,
      null as expire_date
      from m_stock_cur t1
      where t1.is_deleted = 'N' and t1.num > 0
      group by t1.sku_id, t1.wh_id
      ) t6 ON t1.sku_id = t6.sku_id AND t5.wh_id = t6.wh_id
      LEFT JOIN m_batch t7 ON t1.batch_no = t7.batch_no AND t7.is_deleted = 'N'
      LEFT JOIN (
      select
      t1.sku_id,
      t1.wh_id,
      t1.num,
      t1.batch_no,
      t2.price,
      t2.prod_no,
      t2.prod_date,
      t2.expire_date
      from m_stock_cur t1
      inner join m_batch t2 on t1.`batch_no` = t2.`batch_no` and t2.is_deleted = 'N'
      where t1.is_deleted = 'N' and t1.num > 0
      ) t8 on t1.sku_id = t8.sku_id AND t1.batch_no = t8.batch_no AND t5.wh_id = t8.wh_id
      WHERE t1.outb_id = #{outbId}
      <if test="name != null and name != ''">
          AND (
          t2.name LIKE CONCAT('%', #{name}, '%')
          OR t2.flow_no LIKE CONCAT('%', #{name}, '%')
          )
      </if>
      AND t1.is_deleted = 'N'
      ORDER BY t1.create_date ASC
  </select>
</mapper>