package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.returnOrder.ReturnInsertReq;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.constant.MethodCodeConst;
import cn.trasen.ams.material.model.*;
import cn.trasen.ams.material.service.MethodCodeService;
import cn.trasen.ams.material.service.MtdCodeRelaService;
import cn.trasen.ams.material.service.ReturnDtlService;
import cn.trasen.ams.material.service.StockCurService;
import cn.trasen.ams.material.service.StockSeqService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.ReturnMapper;
import cn.trasen.ams.material.model.Return;
import cn.trasen.ams.material.service.ReturnService;
import com.alibaba.fastjson.JSON;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ReturnServiceImpl
 * @Description 退库单服务实现类
 * @date 2025年8月7日 下午4:21:34
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ReturnServiceImpl implements ReturnService {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnServiceImpl.class);

	@Autowired
	private ReturnMapper mapper;

	@Autowired
	private ReturnDtlService returnDtlService;

	@Autowired
	private WarehouseService warehouseService;

	@Autowired
	private MethodCodeService methodCodeService;

	@Autowired
	private MtdCodeRelaService mtdCodeRelaService;

	@Autowired
	private StockCurService stockCurService;

	@Autowired
	private StockSeqService stockSeqService;

	@Autowired
	private DictService dictService;

	@Autowired
	private OrgService orgService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(Return record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(Return record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		Return record = new Return();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public Return selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<Return> getDataSetList(Page page, Return record) {
		Example example = new Example(Return.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<Return> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @param record:
	 * @return void
	 * <AUTHOR>
	 * @description 编辑和删除的准备工作
	 * @date 2025/8/7 16:50
	 */
	@Transactional(readOnly = false)
	public void prepare(ReturnInsertReq record) {
		Return returnOrder = record.getReturnOrder();
		List<ReturnDtl> returnDtlList = record.getReturnDtlList();

		// 新增
		if (returnOrder.getId() == null) {
			// 根据方式码进行流水号生成
			if (returnOrder.getInbId() != null) {
				// 如果有关联的入库单，可以生成相关的流水号
				// 这里可以根据业务需求自定义流水号生成规则
				String flowNo = "TK" + System.currentTimeMillis();
				returnOrder.setFlowNo(flowNo);
			}
			returnOrder.setId(IdGeneraterUtils.nextId());
			// 默认状态
			returnOrder.setStat(OrdConst.ORD_STAT_REGED);
			returnOrder.setPrintStat(CommonConst.NO);
		} else {
			// 删除之前的明细
			returnDtlService._deleteByReturnId_(returnOrder.getId());
		}

		// 计算总金额
		BigDecimal totalAmt = BigDecimal.ZERO;
		for (ReturnDtl returnDtl : returnDtlList) {
			returnDtl.setReturnId(returnOrder.getId());
			returnDtl.setId(IdGeneraterUtils.nextId());

			// 计算明细总金额
			if (returnDtl.getPrice() != null && returnDtl.getNum() != null) {
				BigDecimal dtlTotalAmt = returnDtl.getPrice().multiply(new BigDecimal(returnDtl.getNum()));
				returnDtl.setTotalAmt(dtlTotalAmt);
				totalAmt = totalAmt.add(dtlTotalAmt);
			}
		}

		// 设置退库单的总金额
		returnOrder.setTotalAmt(totalAmt);
	}

	@Transactional(readOnly = false)
	@Override
	public String insert(ReturnInsertReq record) {
		// 准备部分
		prepare(record);

		Return returnOrder = record.getReturnOrder();
		List<ReturnDtl> returnDtlList = record.getReturnDtlList();

		// 写入退库单
		save(returnOrder);

		// 写入退库单详情
		returnDtlService.batchInsert(returnDtlList);
		return returnOrder.getId();
	}

	@Transactional(readOnly = false)
	@Override
	public String edit(ReturnInsertReq record) {
		// 准备部分
		prepare(record);

		Return returnOrder = record.getReturnOrder();
		List<ReturnDtl> returnDtlList = record.getReturnDtlList();
		// 写入退库单
		update(returnOrder);
		// 写入退库单详情
		returnDtlService.batchInsert(returnDtlList);
		return returnOrder.getId();
	}

	@Transactional(readOnly = false)
	@Override
	public void remove(String returnId) {
		Assert.hasText(returnId, "退库单ID不能为空.");
		Return returnOrder = selectById(returnId);

		if (OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
			throw new IllegalArgumentException("已确认的退库单不能删除，请先回滚确认。");
		}

		deleteById(returnId);
		// 删除退库单明细
		returnDtlService.deleteByReturnId(returnId);
	}

	@Transactional(readOnly = false)
	@Override
	public void batchRemove(List<String> returnIdList) {
		if (CollectionUtils.isEmpty(returnIdList)) {
			logger.warn("批量删除退库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		// 逐个处理每个退库单的删除
		for (String returnId : returnIdList) {
			try {
				remove(returnId);
			} catch (Exception e) {
				// 记录错误但继续处理其他单据
				throw new RuntimeException("退库单 " + returnId + " 删除失败: " + e.getMessage(), e);
			}
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void confirm(String returnId) {
		Assert.hasText(returnId, "退库单ID不能为空.");

		Return returnOrder = selectById(returnId);
		if (returnOrder == null) {
			throw new IllegalArgumentException("退库单不存在");
		}

		if (OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
			throw new IllegalArgumentException("退库单已确认，不能重复确认");
		}

		// 获取退库单明细
		List<ReturnDtl> returnDtlList = returnDtlService.getReturnDtlListByReturnId(returnId);
		if (CollectionUtils.isEmpty(returnDtlList)) {
			throw new IllegalArgumentException("退库单明细数据缺失，无法完成确认");
		}

		// 更新库存 - 退库时增加库存
		List<StockCur> stockCurList = new ArrayList<>();
		List<StockSeq> stockSeqList = new ArrayList<>();

		for (ReturnDtl returnDtl : returnDtlList) {
			// 构建库存当前量记录
			StockCur stockCur = new StockCur();
			stockCur.setWhId(returnOrder.getWhId());
			stockCur.setSkuId(returnDtl.getSkuId());
			stockCur.setBatchNo(returnDtl.getBatchNo());
			stockCur.setNum(returnDtl.getNum()); // 退库增加库存
			stockCur.setPrice(returnDtl.getPrice());
			stockCur.setTotalAmt(returnDtl.getTotalAmt());
			stockCurList.add(stockCur);

			// 构建库存流水记录
			StockSeq stockSeq = new StockSeq();
			stockSeq.setWhId(returnOrder.getWhId());
			stockSeq.setSkuId(returnDtl.getSkuId());
			stockSeq.setBatchNo(returnDtl.getBatchNo());
			stockSeq.setOrdType(OrdConst.ORD_TYPE_TH); // 退库类型
			stockSeq.setOrdId(returnId);
			stockSeq.setOrdDtlId(returnDtl.getId());
			stockSeq.setInNum(returnDtl.getNum()); // 退库作为入库处理
			stockSeq.setOutNum(0);
			stockSeq.setPrice(returnDtl.getPrice());
			stockSeq.setTotalAmt(returnDtl.getTotalAmt());
			stockSeqList.add(stockSeq);
		}

		// 更新库存
		stockCurService.updateStock(stockCurList);
		// 插入库存流水
		stockSeqService.batchInsert(stockSeqList);

		// 更新退库单状态
		returnOrder.setStat(OrdConst.ORD_STAT_CHECKED);
		returnOrder.setDoTime(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			returnOrder.setDoerId(user.getUsercode());
			returnOrder.setDoerName(user.getUsername());
		}
		update(returnOrder);

		logger.info("退库单 {} 确认成功", returnId);
	}

	@Transactional(readOnly = false)
	@Override
	public void batchConfirm(List<String> returnIdList) {
		if (CollectionUtils.isEmpty(returnIdList)) {
			logger.warn("批量确认退库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		// 逐个处理每个退库单的确认
		for (String returnId : returnIdList) {
			try {
				confirm(returnId);
			} catch (Exception e) {
				// 记录错误但继续处理其他单据
				throw new RuntimeException("退库单 " + returnId + " 确认失败: " + e.getMessage(), e);
			}
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void rollbackConfirm(String returnId) {
		Assert.hasText(returnId, "退库单ID不能为空.");

		Return returnOrder = selectById(returnId);
		if (returnOrder == null) {
			throw new IllegalArgumentException("退库单不存在");
		}

		// 检查退库单状态，只有已确认的单据才能回滚
		if (!OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
			throw new IllegalArgumentException("只有已确认的退库单才能进行回滚操作");
		}

		// 获取退库单明细
		List<ReturnDtl> returnDtlList = returnDtlService.getReturnDtlListByReturnId(returnId);
		if (CollectionUtils.isEmpty(returnDtlList)) {
			throw new IllegalArgumentException("退库单明细数据缺失，无法完成回滚");
		}

		// 回滚库存 - 退库回滚时减少库存
		List<StockCur> stockCurList = new ArrayList<>();

		for (ReturnDtl returnDtl : returnDtlList) {
			// 构建库存当前量记录（负数表示减少）
			StockCur stockCur = new StockCur();
			stockCur.setWhId(returnOrder.getWhId());
			stockCur.setSkuId(returnDtl.getSkuId());
			stockCur.setBatchNo(returnDtl.getBatchNo());
			stockCur.setNum(-returnDtl.getNum()); // 回滚时减少库存
			stockCur.setPrice(returnDtl.getPrice());
			stockCur.setTotalAmt(returnDtl.getTotalAmt().negate()); // 负数金额
			stockCurList.add(stockCur);
		}

		// 更新库存
		stockCurService.updateStock(stockCurList);

		// 删除相关的库存流水记录
		stockSeqService.deleteByOrdId(returnId);

		// 更新退库单状态为已登记
		returnOrder.setStat(OrdConst.ORD_STAT_REGED);
		returnOrder.setDoTime(null);
		returnOrder.setDoerId(null);
		returnOrder.setDoerName(null);
		update(returnOrder);

		logger.info("退库单 {} 回滚确认成功", returnId);
	}

	@Transactional(readOnly = false)
	@Override
	public void rollbackBatchConfirm(List<String> returnIdList) {
		if (CollectionUtils.isEmpty(returnIdList)) {
			logger.warn("批量撤销确认退库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		// 逐个处理每个退库单的回滚
		for (String returnId : returnIdList) {
			try {
				rollbackConfirm(returnId);
			} catch (Exception e) {
				// 记录错误但继续处理其他单据
				throw new RuntimeException("退库单 " + returnId + " 回滚失败: " + e.getMessage(), e);
			}
		}
	}

	@Override
	public String getTargetReturnId(String currentId, String direction) {
		// 这里可以实现获取上一条/下一条退库单的逻辑
		// 暂时返回当前ID
		return currentId;
	}

	@Override
	public void dataFmt(Return record) {
		if (record == null) {
			return;
		}

		// 格式化状态显示
		if (!StringUtils.isEmpty(record.getStat())) {
			record.setStat(dictService.cgetNameByValue(OrdConst.ORD_STAT, record.getStat()));
		}

		// 格式化打印状态显示
		if (!StringUtils.isEmpty(record.getPrintStat())) {
			record.setPrintStat(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getPrintStat()));
		}

		// 可以添加更多的数据格式化逻辑
	}
}
