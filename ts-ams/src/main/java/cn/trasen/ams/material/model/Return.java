package cn.trasen.ams.material.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "m_return")
@Setter
@Getter
public class Return {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 入库单ID
     */
    @Column(name = "inb_id")
    @ApiModelProperty(value = "入库单ID")
    private String inbId;

    /**
     * 单据号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "单据号")
    private String flowNo;

    /**
     * 退货仓库ID
     */
    @Column(name = "wh_id")
    @ApiModelProperty(value = "退货仓库ID")
    private String whId;

    /**
     * 供应商ID
     */
    @Column(name = "supply_id")
    @ApiModelProperty(value = "供应商ID")
    private String supplyId;

    /**
     * 退货人ID
     */
    @Column(name = "returner_id")
    @ApiModelProperty(value = "退货人ID")
    private String returnerId;

    /**
     * 退货人名称
     */
    @Column(name = "returner_name")
    @ApiModelProperty(value = "退货人名称")
    private String returnerName;

    /**
     * 退货金额
     */
    @Column(name = "total_amt")
    @ApiModelProperty(value = "退货金额")
    private BigDecimal totalAmt;

    /**
     * 状态 1 已登记 2 已确认
     */
    @ApiModelProperty(value = "状态 1 已登记 2 已确认")
    private String stat;

    /**
     * 打印状态 0 未打印 1 已打印
     */
    @Column(name = "print_stat")
    @ApiModelProperty(value = "打印状态 0 未打印 1 已打印")
    private String printStat;

    /**
     * 退货日期
     */
    @Column(name = "return_date")
    @ApiModelProperty(value = "退货日期")
    private Date returnDate;

    /**
     * 审核人ID
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "审核人ID")
    private String doerId;

    /**
     * 审核人名称
     */
    @Column(name = "doer_name")
    @ApiModelProperty(value = "审核人名称")
    private String doerName;

    /**
     * 审核人名称
     */
    @Column(name = "do_time")
    @ApiModelProperty(value = "审核人名称")
    private Date doTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}