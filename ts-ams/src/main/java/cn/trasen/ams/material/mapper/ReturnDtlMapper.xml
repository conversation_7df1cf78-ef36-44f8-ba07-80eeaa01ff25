<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.ReturnDtlMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.ReturnDtl">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="return_id" jdbcType="VARCHAR" property="returnId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <resultMap id="ExtResultMap" type="cn.trasen.ams.material.bean.returnOrder.ReturnDtlResp" extends="BaseResultMap">
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="reg_no" jdbcType="VARCHAR" property="regNo" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="manufacturer_name" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="prod_no" jdbcType="VARCHAR" property="prodNo" />
    <result column="prod_date" jdbcType="DATE" property="prodDate" />
    <result column="expire_date" jdbcType="DATE" property="expireDate" />
  </resultMap>

  <!-- 批量插入退库单明细 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO m_return_dtl (
      id, return_id, sku_id, batch_no, price, num, total_amt,
      create_date, create_user, create_user_name, dept_id, dept_name,
      update_date, update_user, update_user_name, sso_org_code, sso_org_name, is_deleted
    ) VALUES
    <foreach collection="returnDtlList" item="item" separator=",">
      (
        #{item.id}, #{item.returnId}, #{item.skuId}, #{item.batchNo}, #{item.price}, #{item.num}, #{item.totalAmt},
        #{item.createDate}, #{item.createUser}, #{item.createUserName}, #{item.deptId}, #{item.deptName},
        #{item.updateDate}, #{item.updateUser}, #{item.updateUserName}, #{item.ssoOrgCode}, #{item.ssoOrgName}, #{item.isDeleted}
      )
    </foreach>
  </insert>

  <!-- 根据退库单ID获取扩展明细列表 -->
  <select id="getReturnDtlExtListByReturnId" resultMap="ExtResultMap">
    SELECT
      rd.id, rd.return_id, rd.sku_id, rd.batch_no, rd.price, rd.num, rd.total_amt,
      rd.create_date, rd.create_user, rd.create_user_name, rd.dept_id, rd.dept_name,
      rd.update_date, rd.update_user, rd.update_user_name, rd.sso_org_code, rd.sso_org_name, rd.is_deleted,
      ms.flow_no, ms.name, ms.model, ms.unit, ms.origin_price, ms.reg_no, ms.brand,
      mf.name as manufacturer_name,
      mc.name as category_name,
      b.prod_no, b.prod_date, b.expire_date
    FROM m_return_dtl rd
    LEFT JOIN m_sku ms ON rd.sku_id = ms.id AND ms.is_deleted = 'N'
    LEFT JOIN m_manufacturer mf ON ms.manufacturer_id = mf.id AND mf.is_deleted = 'N'
    LEFT JOIN c_category mc ON ms.category_id = mc.id AND mc.is_deleted = 'N'
    LEFT JOIN m_batch b ON rd.batch_no = b.batch_no AND b.is_deleted = 'N'
    WHERE rd.return_id = #{returnId}
      AND rd.is_deleted = 'N'
      <if test="name != null and name != ''">
        AND ms.name LIKE CONCAT('%', #{name}, '%')
      </if>
    ORDER BY rd.create_date DESC
  </select>
</mapper>