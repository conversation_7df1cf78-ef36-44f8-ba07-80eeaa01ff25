package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnDtl;
import cn.trasen.ams.material.service.ReturnDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName ReturnDtlController
 * @Description TODO
 * @date 2025年8月7日 下午4:21:52
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "ReturnDtlController")
public class ReturnDtlController {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnDtlController.class);

	@Autowired
	private ReturnDtlService returnDtlService;

	/**
	 * @Title saveReturnDtl
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/returnDtl/save")
	public PlatformResult<String> saveReturnDtl(@RequestBody ReturnDtl record) {
		try {
			returnDtlService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateReturnDtl
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/returnDtl/update")
	public PlatformResult<String> updateReturnDtl(@RequestBody ReturnDtl record) {
		try {
			returnDtlService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectReturnDtlById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<ReturnDtl>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/returnDtl/{id}")
	public PlatformResult<ReturnDtl> selectReturnDtlById(@PathVariable String id) {
		try {
			ReturnDtl record = returnDtlService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteReturnDtlById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/returnDtl/delete/{id}")
	public PlatformResult<String> deleteReturnDtlById(@PathVariable String id) {
		try {
			returnDtlService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectReturnDtlList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<ReturnDtl>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/returnDtl/list")
	public DataSet<ReturnDtl> selectReturnDtlList(Page page, ReturnDtl record) {
		return returnDtlService.getDataSetList(page, record);
	}
}
