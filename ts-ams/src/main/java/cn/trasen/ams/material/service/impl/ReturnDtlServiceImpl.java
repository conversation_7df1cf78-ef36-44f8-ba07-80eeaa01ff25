package cn.trasen.ams.material.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.ReturnDtlMapper;
import cn.trasen.ams.material.model.ReturnDtl;
import cn.trasen.ams.material.service.ReturnDtlService;
import cn.trasen.ams.material.bean.returnOrder.ReturnDtlResp;
import cn.trasen.ams.material.constant.MSkuConst;
import cn.trasen.ams.common.service.DictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ReturnDtlServiceImpl
 * @Description 退库单明细服务实现类
 * @date 2025年8月7日 下午4:21:52
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ReturnDtlServiceImpl implements ReturnDtlService {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnDtlServiceImpl.class);

	@Autowired
	private ReturnDtlMapper mapper;

	@Autowired
	private DictService dictService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(ReturnDtl record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(ReturnDtl record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		ReturnDtl record = new ReturnDtl();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public ReturnDtl selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<ReturnDtl> getDataSetList(Page page, ReturnDtl record) {
		Example example = new Example(ReturnDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<ReturnDtl> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void batchInsert(List<ReturnDtl> returnDtlList) {
		if (returnDtlList == null || returnDtlList.isEmpty()) {
			return;
		}

		// 为每个明细设置基础信息
		Date now = new Date();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();

		for (ReturnDtl returnDtl : returnDtlList) {
			if (returnDtl.getId() == null) {
				returnDtl.setId(IdGeneraterUtils.nextId());
			}
			returnDtl.setCreateDate(now);
			returnDtl.setUpdateDate(now);
			returnDtl.setIsDeleted("N");

			if (user != null) {
				returnDtl.setCreateUser(user.getUsercode());
				returnDtl.setCreateUserName(user.getUsername());
				returnDtl.setUpdateUser(user.getUsercode());
				returnDtl.setUpdateUserName(user.getUsername());
				returnDtl.setSsoOrgCode(user.getCorpcode());
				returnDtl.setSsoOrgName(user.getOrgName());
				returnDtl.setDeptId(user.getDeptId());
				returnDtl.setDeptName(user.getDeptname());
			}
		}

		// 批量插入
		mapper.batchInsert(returnDtlList);
	}

	@Transactional(readOnly = false)
	@Override
	public void deleteByReturnId(String returnId) {
		// 逻辑删除
		Assert.hasText(returnId, "退库单ID不能为空.");
		Example example = new Example(ReturnDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnId", returnId);

		ReturnDtl record = new ReturnDtl();
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}

		// 执行逻辑删除
		int deletedCount = mapper.updateByExampleSelective(record, example);

		// 记录删除结果
		if (deletedCount > 0) {
			logger.info("成功逻辑删除退库单 {} 的 {} 条明细记录", returnId, deletedCount);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void _deleteByReturnId_(String returnId) {
		// 物理删除
		Assert.hasText(returnId, "退库单ID不能为空.");
		Example example = new Example(ReturnDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnId", returnId);

		// 执行物理删除
		int deletedCount = mapper.deleteByExample(example);

		// 记录删除结果
		if (deletedCount > 0) {
			logger.info("成功删除退库单 {} 的 {} 条明细记录", returnId, deletedCount);
		}
	}

	@Override
	public List<ReturnDtl> getReturnDtlListByReturnId(String returnId) {
		Example example = new Example(ReturnDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnId", returnId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<ReturnDtl> records = mapper.selectByExample(example);
		return records;
	}

	@Override
	public List<ReturnDtlResp> getReturnDtlExtListByReturnId(String returnId, String name) {
		List<ReturnDtlResp> returnDtlRespList = mapper.getReturnDtlExtListByReturnId(returnId, name);
		// dataFmt(returnDtlRespList);
		returnDtlRespList.forEach(this::dataFmt);
		return returnDtlRespList;
	}

	@Override
	public void dataFmt(ReturnDtlResp record) {
		if (record == null) {
			return;
		}
		record.setUnitShow(dictService.cgetNameByValue(MSkuConst.SKU_UNIT, record.getUnit()));
	}
}
