package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnDtl;

/**
 * @ClassName ReturnDtlService
 * @Description TODO
 * @date 2025年8月7日 下午4:21:52
 * <AUTHOR>
 * @version 1.0
 */
public interface ReturnDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer save(ReturnDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer update(ReturnDtl record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return ReturnDtl
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	ReturnDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<ReturnDtl>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	DataSet<ReturnDtl> getDataSetList(Page page, ReturnDtl record);
}
