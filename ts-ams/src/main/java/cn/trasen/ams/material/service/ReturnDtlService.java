package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnDtl;
import cn.trasen.ams.material.bean.returnOrder.ReturnDtlResp;

import java.util.List;

/**
 * @ClassName ReturnDtlService
 * @Description 退库单明细服务接口
 * @date 2025年8月7日 下午4:21:52
 * <AUTHOR>
 * @version 1.0
 */
public interface ReturnDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer save(ReturnDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer update(ReturnDtl record);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return ReturnDtl
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	ReturnDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<ReturnDtl>
	 * @date 2025年8月7日 下午4:21:52
	 * <AUTHOR>
	 */
	DataSet<ReturnDtl> getDataSetList(Page page, ReturnDtl record);

	/**
	 * @param returnDtlList
	 * @return void
	 * @Title batchInsert
	 * @Description 批量插入退库单明细
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	void batchInsert(List<ReturnDtl> returnDtlList);

	/**
	 * @param returnId
	 * @return void
	 * @Title deleteByReturnId
	 * @Description 根据退库单ID删除明细（逻辑删除）
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	void deleteByReturnId(String returnId);

	/**
	 * @param returnId
	 * @return void
	 * @Title _deleteByReturnId_
	 * @Description 根据退库单ID删除明细（物理删除）
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	void _deleteByReturnId_(String returnId);

	/**
	 * @param returnId
	 * @return List<ReturnDtl>
	 * @Title getReturnDtlListByReturnId
	 * @Description 根据退库单ID获取明细列表
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	List<ReturnDtl> getReturnDtlListByReturnId(String returnId);

	/**
	 * @param returnId
	 * @param name
	 * @return List<ReturnDtlResp>
	 * @Title getReturnDtlExtListByReturnId
	 * @Description 根据退库单ID获取扩展明细列表
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	List<ReturnDtlResp> getReturnDtlExtListByReturnId(String returnId, String name);

	/**
	 * @param record
	 * @return void
	 * @Title dataFmt
	 * @Description 数据格式化
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	void dataFmt(ReturnDtlResp record);
}
